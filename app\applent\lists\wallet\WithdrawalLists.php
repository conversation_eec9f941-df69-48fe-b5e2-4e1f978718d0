<?php

namespace app\applent\lists\wallet;

use app\applent\lists\BaseApiDataLists;
use app\common\lists\ListsSearchInterface;
use app\common\model\withdrawal\WithdrawalRecord;
use app\common\service\ConfigService;
use app\common\model\pay\PayConfig;

/**
 * 钱包提现明细列表
 * Class WithdrawalLists
 * @package app\applent\lists\wallet
 */
class WithdrawalLists extends BaseApiDataLists implements ListsSearchInterface
{
    /**
     * @notes 搜索条件
     * @return array
     */
    public function setSearch(): array
    {
        $searchFields = ['date'];
        //获取两个数组交集
        return array_intersect(array_keys($this->params), $searchFields);
    }

    /**
     * @notes 设置列表字段
     * @return array
     */
    public function setFields(): array
    {
        return [
            'id',
            'withdrawal_no',
            'exchange_amount',
            'status',
            'withdrawal_type',
            'create_time',
        ];
    }

    /**
     * @notes 设置排序
     * @return array
     */
    public function setOrder(): array
    {
        return ['create_time' => 'desc'];
    }

    /**
     * @notes 获取列表数据
     * @return array
     */
    public function lists(): array
    {
        
        $lists = WithdrawalRecord::field($this->setFields())
            ->where(['user_id' => $this->userId])
            ->withSearch($this->setSearch(), $this->params)
            ->order($this->setOrder())
            ->page($this->pageNo, $this->pageSize)
            ->append(['status_text', 'withdrawal_type_text'])
            ->select()
            ->toArray();

        return $this->createReturn($lists);
    }

    /**
     * @notes 获取数量
     * @return int
     */
    public function count(): int
    {
        return WithdrawalRecord::where(['user_id' => $this->userId])
            ->withSearch($this->setSearch(), $this->params)
            ->count();
    }

    /**
     * @notes 处理列表数据
     * @param array $lists
     * @return array
     */
    public function createReturn(array $lists): array
    {
        $data = [];
        $currencyName = ConfigService::get('systemconfig', 'profit_name', '钻石');

        foreach ($lists as $item) {
            // 根据status字段处理不同的显示逻辑
            $processedItem = $this->processItemByStatus($item, $currencyName);
            $data[] = $processedItem;
        }

        return $data;
    }

    /**
     * @notes 处理提现订单数据显示
     * @param array $item
     * @param string $currencyName
     * @return array
     */
    private function processItemByStatus($item, $currencyName)
    {
        // 根据status判断金额的正负号
        // 0=待审核, 1=审核通过 显示负号(-)
        // 2=审核驳回 显示正号(+)
        $amountPrefix = ($item['status'] == 2) ? '+' : '-';

        $result = [
            'id'            => $item['id'],
            'title'         => rtrim($item['withdrawal_type_text'], '号') . '提现',
            'amount'        => $amountPrefix . $item['exchange_amount'],
            'currency'      => $currencyName,
            'status'        => $item['status'],
            'status_text'   => $item['status_text'],
            'create_time'   => $item['create_time'],
            'withdrawal_no' => $item['withdrawal_no'],
        ];

        return $result;
    }
}
