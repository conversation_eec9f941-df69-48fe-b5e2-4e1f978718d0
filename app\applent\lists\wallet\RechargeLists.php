<?php

namespace app\applent\lists\wallet;

use app\applent\lists\BaseApiDataLists;
use app\common\lists\ListsSearchInterface;
use app\common\model\recharge\RechargeOrder;
use app\common\model\pay\PayConfig;
use app\common\service\ConfigService;

/**
 * 钱包充值明细列表
 * Class RechargeLists
 * @package app\applent\lists\wallet
 */
class RechargeLists extends BaseApiDataLists implements ListsSearchInterface
{
    /**
     * @notes 搜索条件
     * @return array
     */
    public function setSearch(): array
    {
        $searchFields = ['date'];
        //获取两个数组交集
        return array_intersect(array_keys($this->params), $searchFields);
    }

    /**
     * @notes 设置列表字段
     * @return array
     */
    public function setFields(): array
    {
        return [
            'id',
            'order_no',
            'pay_method_id',
            'total_coin',
            'pay_status',
            'update_time',
        ];
    }

    /**
     * @notes 设置排序
     * @return array
     */
    public function setOrder(): array
    {
        return ['create_time' => 'desc'];
    }

    /**
     * @notes 获取列表数据
     * @return array
     */
    public function lists(): array
    {
        
        $lists = RechargeOrder::field($this->setFields())
            ->where(['user_id' => $this->userId,'pay_status'=>2])
            ->withSearch($this->setSearch(), $this->params)
            ->order($this->setOrder())
            ->page($this->pageNo, $this->pageSize)
            ->select()
            ->toArray();

        return $this->createReturn($lists);
    }

    /**
     * @notes 获取数量
     * @return int
     */
    public function count(): int
    {
        return RechargeOrder::where(['user_id' => $this->userId, 'pay_status' => 2])
            ->withSearch($this->setSearch(), $this->params)
            ->count();
    }

    /**
     * @notes 处理列表数据
     * @param array $lists
     * @return array
     */
    public function createReturn(array $lists): array
    {
        $data = [];
        $currencyName = ConfigService::get('systemconfig', 'currency_name', '金币');

        foreach ($lists as $item) {
            // 根据source字段处理不同的显示逻辑
            $processedItem = $this->processItemBySource($item, $currencyName);
            $data[] = $processedItem;
        }

        return $data;
    }

    /**
     * @notes 处理充值订单数据显示
     * @param array $item
     * @param string $currencyName
     * @return array
     */
    private function processItemBySource($item, $currencyName)
    {
        // 获取支付方式名称
        $payConfig = PayConfig::where('id', $item['pay_method_id'])->find();
        $payMethodName = $payConfig ? $payConfig->name : '未知支付方式';

        $result = [
            'id'            => $item['id'],
            'title'         => $payMethodName . '充值',
            'amount'        => '+' . $item['total_coin'],
            'currency'      => $currencyName,
            'status_text'   =>'充值成功',
            'create_time'   => $item['update_time'],
            'order_no'      => $item['order_no'],
        ];

        return $result;
    }
}
