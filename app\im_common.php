<?php
use app\common\service\tim\TencentImService;
use app\common\model\user\User;

//获取IM配置
function get_im_config($user_id){
    $imService = new TencentImService();
    $imConfig = $imService->getBaseConfig($user_id);
    return $imConfig;
}

//创建IM用户
function create_im_user_info($user_id){
    $userInfo = User::field('id,nickname,avatar')->find($user_id);
    $imService = new TencentImService();
    $imResult = $imService->createAccount(
        (string)$user_id,
        $userInfo['nickname'],
        $userInfo['avatar'],
    );
    return $imResult;
}

//修改IM用户资料
function update_im_user_info($user_id){
    $userInfo = User::field('id,nickname,avatar')->find($user_id);
    $imService = new TencentImService();
    $imResult = $imService->updateAccount(
        (string)$user_id,
        $userInfo['nickname'],
        $userInfo['avatar'],
    );
    return $imResult;
}

//发送文本消息
function send_im_text_msg($user_id, $to_user_id, $msg){
    //构建消息结构
    $msgBody[] = [
        'MsgType' => 'TIMTextElem',
        'MsgContent' => ['Text' => $msg]
    ];

    $imService = new TencentImService();
    $imResult = $imService->sendCustomMessage(
        (string)$user_id,
        (string)$to_user_id,
        $msgBody,
    );
    return $imResult;
}

//type 14 一对一视频消息挂断推送
function end_video_call($user_id, $to_user_id, $video_call_info)
{

    $ext['type'] = 14;//14;
    $sender['id'] = $user_id;
    $sender['user_nickname'] = 'admin';
    $sender['avatar'] = '';
    $ext['channel'] = 1; //通话频道
    $ext['sender'] = $sender;
    $ext['reply_type'] = 1;
    
    //构建消息结构
    $msgBody[] = array(
        'MsgType'    => 'TIMCustomElem',       //自定义类型
        'MsgContent' => array(
            'Data' => json_encode($ext),
            'Desc' => '',
        )
    );
    
    $imService = new TencentImService();
    $imResult = $imService->sendCustomMessage(
        (string)$user_id,
        (string)$to_user_id,
        $msgBody,
    );
    return $imResult;
}

