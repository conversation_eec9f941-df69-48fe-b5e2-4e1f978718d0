<?php

namespace app\applent\lists\wallet;

use app\applent\lists\BaseApiDataLists;
use app\common\lists\ListsSearchInterface;
use app\common\model\user\UserIncomeLog;
use app\common\service\ConfigService;

/**
 * 钱包收入明细列表
 * Class IncomeLists
 * @package app\applent\lists\wallet
 */
class IncomeLists extends BaseApiDataLists implements ListsSearchInterface
{
    /**
     * @notes 搜索条件
     * @return array
     */
    public function setSearch(): array
    {
        $searchFields = ['date'];
        //获取两个数组交集
        return array_intersect(array_keys($this->params), $searchFields);
    }

    /**
     * @notes 设置列表字段
     * @return array
     */
    public function setFields(): array
    {
        return [
            'id',
            'source_no',
            'to_user_id',
            'to_nickname',
            'type',
            'change_income',
            'source',
            'gift_num',
            'gift_price',
            'gift_name',
            'audio_num',
            'audio_price',
            'create_time',
        ];
    }

    /**
     * @notes 设置排序
     * @return array
     */
    public function setOrder(): array
    {
        return ['create_time' => 'desc'];
    }

    /**
     * @notes 获取列表数据
     * @return array
     */
    public function lists(): array
    {
        
        $lists = UserIncomeLog::field($this->setFields())
            ->where(['user_id' => $this->userId, 'type' => 1])
            ->where('source', '<>', 10)
            ->withSearch($this->setSearch(), $this->params)
            ->order($this->setOrder())
            ->page($this->pageNo, $this->pageSize)
            ->select()
            ->toArray();
            
        return $this->createReturn($lists);
    }

    /**
     * @notes 获取数量
     * @return int
     */
    public function count(): int
    {
        return UserIncomeLog::where(['user_id' => $this->userId, 'type' => 1])
            ->withSearch($this->setSearch(), $this->params)
            ->count();
    }

    /**
     * @notes 处理列表数据
     * @param array $lists
     * @return array
     */
    public function createReturn(array $lists): array
    {
        $data = [];
        $currencyName = ConfigService::get('systemconfig', 'profit_name', '钻石');

        foreach ($lists as $item) {
            // 根据source字段处理不同的显示逻辑
            $processedItem = $this->processItemBySource($item, $currencyName);
            $data[] = $processedItem;
        }

        return $data;
    }

    /**
     * @notes 根据source处理不同的数据显示
     * @param array $item
     * @param string $currencyName
     * @return array
     */
    private function processItemBySource($item, $currencyName)
    {
        $result = [
            'id' => $item['id'],
            'source_no' => $item['source_no'],
            'to_user_id' => $item['to_user_id'],
            'to_nickname' => $item['to_nickname'],
            'type' => $item['type'],
            'change_coin' => $item['change_income'],
            'source' => $item['source'],
            'create_time' => $item['create_time'],
            'currency_name' => $currencyName,
        ];

        // 根据source字段设置不同的显示内容
        switch ($item['source']) {
            case 1: // 礼物收益
                $result['title'] = '获得礼物';
                $result['quantity'] = $item['gift_num'];
                $result['unit_price'] = $item['gift_price'] . $currencyName;
                $result['item_name'] = $item['gift_name'];
                break;

            case 2: // 语音视频收益
                $result['title'] = '音频通话';
                $result['quantity'] = $item['audio_num'];
                $result['unit_price'] = $item['audio_price'] . $currencyName;
                $result['item_name'] = '分钟';
                break;

            case 3: // 充值收益一级返佣
            case 4: // 充值收益二级返佣
            case 5: // 礼物收益一级返佣
            case 6: // 礼物收益二级返佣
            case 7: // 语音视频通话一级返佣
            case 8: // 语音视频通话二级返佣
            case 11: // 下级首冲奖励
            case 12: // 会员充值收益一级返佣
            case 13: // 会员充值收益二级返佣
                $result['title'] = '下级返佣';
                $result['quantity'] = 1;
                $result['unit_price'] = '';
                $result['item_name'] = '';
                break;
            default:
                $result['title'] = '其他收入';
                $result['quantity'] = 1;
                $result['unit_price'] = '';
                $result['item_name'] = '';
                break;
        }

        return $result;
    }
}
