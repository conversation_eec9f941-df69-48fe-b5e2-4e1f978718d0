<?php

namespace app\applent\controller;

use app\applent\controller\BaseApiController;
use app\applent\logic\crontab\CrontabLogic;

/**
 * 定时任务控制器
 * Class CrontabController
 * @package app\applent\controller
 */
class CrontabController extends BaseApiController
{
    public array $notNeedLogin = ['handle_timeout_recharge_orders'];
    /**
     * @notes 处理超时充值订单
     * @return \think\response\Json
     */
    public function handle_timeout_recharge_orders()
    {
        $result = CrontabLogic::handleTimeoutRechargeOrders();

        if ($result === false) {
            return $this->fail(CrontabLogic::getError());
        }

        if ($result['processed_count'] == 0) {
            return $this->success('没有需要处理的超时订单', $result);
        }

        return $this->success('超时订单处理完成', $result);
    }
}
